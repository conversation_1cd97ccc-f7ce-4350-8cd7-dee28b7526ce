'use client'

import { useEffect, useRef, useCallback } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useSidebarStore } from '@/stores'

export function Sidebar() {
  const { isOpen, isMobile, setIsOpen, setIsMobile, navigationItems, footerItems } =
    useSidebarStore()
  const pathname = usePathname()
  const sidebarRef = useRef<HTMLDivElement>(null)
  const backdropRef = useRef<HTMLDivElement>(null)

  // Mobile detection with improved logic
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 1024
      const wasMobile = isMobile
      setIsMobile(mobile)

      // Only auto-close if transitioning from desktop to mobile and sidebar is open
      if (!wasMobile && mobile && isOpen) {
        setIsOpen(false)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [setIsMobile, isMobile, isOpen, setIsOpen])

  // Escape key handling
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && isMobile) {
        setIsOpen(false)
      }
    }

    if (isMobile && isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, setIsOpen, isMobile])

  // Click outside handling with improved touch support
  const handleBackdropClick = useCallback(
    (event: MouseEvent | TouchEvent) => {
      if (
        isMobile &&
        isOpen &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node) &&
        backdropRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    },
    [isMobile, isOpen, setIsOpen]
  )

  useEffect(() => {
    if (isMobile && isOpen) {
      // Add both mouse and touch events for better mobile support
      document.addEventListener('mousedown', handleBackdropClick)
      document.addEventListener('touchstart', handleBackdropClick)
      return () => {
        document.removeEventListener('mousedown', handleBackdropClick)
        document.removeEventListener('touchstart', handleBackdropClick)
      }
    }
  }, [isMobile, isOpen, handleBackdropClick])

  // Body scroll lock for mobile
  useEffect(() => {
    if (isMobile && isOpen) {
      const originalOverflow = document.body.style.overflow
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = originalOverflow
      }
    }
  }, [isMobile, isOpen])

  // Focus management for accessibility
  useEffect(() => {
    if (isMobile && isOpen && sidebarRef.current) {
      // Focus the sidebar when it opens on mobile
      const firstFocusableElement = sidebarRef.current.querySelector(
        'a, button, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement
      if (firstFocusableElement) {
        firstFocusableElement.focus()
      }
    }
  }, [isMobile, isOpen])

  const handleNavigationItemClick = () => {
    if (isMobile) {
      setIsOpen(false)
    }
  }

  const handleFooterItemClick = () => {
    if (isMobile) {
      setIsOpen(false)
    }
  }

  return (
    <>
      {/* Backdrop overlay for mobile */}
      {isMobile && isOpen && (
        <div
          ref={backdropRef}
          className="fixed inset-0 bg-black/50 z-40 lg:hidden transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
          onTouchStart={() => setIsOpen(false)}
          aria-hidden="true"
          role="button"
          tabIndex={-1}
          aria-label="Close sidebar"
        />
      )}

      <aside
        ref={sidebarRef}
        className={`
          fixed top-4 bottom-4 left-4 z-50
          w-[var(--layout-sidebar-width)] bg-[var(--layout-sidebar-background)] rounded-box
          flex flex-col shadow-xl border border-base-300
          transition-transform duration-300 ease-in-out
          ${
            isMobile
              ? isOpen
                ? 'translate-x-0'
                : '-translate-x-full'
              : isOpen
              ? 'translate-x-0'
              : '-translate-x-full lg:translate-x-0'
          }
        `}
        aria-label="Main sidebar"
        aria-hidden={isMobile ? !isOpen : false}
        role="navigation"
      >
        {/* Header */}
        <div className="flex min-h-16 items-center justify-between px-4">
          <Link
            href="/"
            className="flex items-center justify-center transition-opacity hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100 rounded-lg"
            aria-label="Go to homepage"
          >
            <Image src="/logo-light.svg" alt="Company logo" width={103} height={20} priority />
          </Link>

          {/* Close button for mobile */}
          {isMobile && (
            <button
              onClick={() => setIsOpen(false)}
              className="btn btn-square btn-ghost btn-sm lg:hidden"
              aria-label="Close sidebar"
            >
              <span
                className="icon-[solar--close-circle-line-duotone] text-lg"
                aria-hidden="true"
              />
            </button>
          )}
        </div>
        {/* Section */}
        <nav className="flex-1 min-h-0 overflow-y-auto px-3 py-4" aria-label="Main navigation">
          <ul className="space-y-1" role="list">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href
              return (
                <li key={item.id}>
                  <Link
                    href={item.href}
                    onClick={handleNavigationItemClick}
                    className={`
                      flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200
                      hover:bg-base-200 focus:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100
                      ${
                        isActive
                          ? 'bg-primary text-primary-content hover:bg-primary/90 focus:bg-primary/90'
                          : 'text-base-content hover:text-base-content focus:text-base-content'
                      }
                    `}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    <span className={`${item.icon} text-lg flex-shrink-0`} aria-hidden="true" />
                    <span className="font-medium truncate">{item.label}</span>
                    {item.badge && (
                      <span
                        className={`
                          ml-auto px-2 py-1 text-xs font-semibold rounded-full flex-shrink-0
                          ${
                            isActive
                              ? 'bg-primary-content/20 text-primary-content'
                              : 'bg-primary text-primary-content'
                          }
                        `}
                        aria-label={`${item.label} has ${item.badge} ${
                          typeof item.badge === 'number' ? 'items' : ''
                        }`}
                      >
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </li>
              )
            })}
          </ul>
        </nav>
        {/* Footer */}
        <div className="px-3 py-4 border-t border-base-300">
          <nav aria-label="Secondary navigation">
            <ul className="space-y-1" role="list">
              {footerItems.map((item) => (
                <li key={item.id}>
                  <Link
                    href={item.href}
                    onClick={handleFooterItemClick}
                    className="
                      flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200
                      text-base-content/70 hover:text-base-content hover:bg-base-200
                      focus:text-base-content focus:bg-base-200 focus:outline-none
                      focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-base-100
                    "
                  >
                    <span className={`${item.icon} text-lg flex-shrink-0`} aria-hidden="true" />
                    <span className="font-medium truncate">{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </aside>
    </>
  )
}
